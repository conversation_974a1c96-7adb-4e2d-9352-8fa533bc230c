<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON> a Timer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .section {
            flex: 1;
            min-width: 300px;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 15px;
        }
        .calculator-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
        }
        .calculator-grid button {
            padding: 10px;
            font-size: 18px;
        }
        #display {
            grid-column: span 4;
            padding: 10px;
            font-size: 24px;
            text-align: right;
            margin-bottom: 10px;
            border: 1px solid #ccc;
        }
        .timer-controls {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .timer-controls button {
            padding: 8px 15px;
        }
    </style>
</head>
<body>
    <h1><PERSON><PERSON><PERSON><PERSON><PERSON> a Timer</h1>
    
    <div class="container">
        <div class="section">
            <h2>Kalkulačka</h2>
            <div class="calculator-grid">
                <div id="display">0</div>
                <button onclick="clearDisplay()">C</button>
                <button onclick="appendToDisplay('/')">/</button>
                <button onclick="appendToDisplay('*')">*</button>
                <button onclick="appendToDisplay('7')">7</button>
                <button onclick="appendToDisplay('8')">8</button>
                <button onclick="appendToDisplay('9')">9</button>
                <button onclick="appendToDisplay('-')">-</button>
                <button onclick="appendToDisplay('4')">4</button>
                <button onclick="appendToDisplay('5')">5</button>
                <button onclick="appendToDisplay('6')">6</button>
                <button onclick="appendToDisplay('+')">+</button>
                <button onclick="appendToDisplay('1')">1</button>
                <button onclick="appendToDisplay('2')">2</button>
                <button onclick="appendToDisplay('3')">3</button>
                <button onclick="calculate()" style="grid-row: span 2">=</button>
                <button onclick="appendToDisplay('0')" style="grid-column: span 2">0</button>
                <button onclick="appendToDisplay('.')">.</button>
            </div>
        </div>
        
        <div class="section">
            <h2>Timer</h2>
            <div>
                <input type="number" id="minutes" min="0" max="59" value="0">
                <label for="minutes">minut</label>
                <input type="number" id="seconds" min="0" max="59" value="0">
                <label for="seconds">sekund</label>
            </div>
            <div class="timer-display">
                <h3 id="time-left">00:00</h3>
            </div>
            <div class="timer-controls">
                <button id="start-btn" onclick="startTimer()">Start</button>
                <button id="pause-btn" onclick="pauseTimer()" disabled>Pauza</button>
                <button id="reset-btn" onclick="resetTimer()">Reset</button>
            </div>
        </div>
    </div>

    <script>
        // Kalkulačka
        let displayValue = '0';
        
        function updateDisplay() {
            document.getElementById('display').textContent = displayValue;
        }
        
        function appendToDisplay(value) {
            if (displayValue === '0' && value !== '.') {
                displayValue = value;
            } else {
                displayValue += value;
            }
            updateDisplay();
        }
        
        function clearDisplay() {
            displayValue = '0';
            updateDisplay();
        }
        
        function calculate() {
            try {
                displayValue = eval(displayValue).toString();
            } catch (error) {
                displayValue = 'Chyba';
            }
            updateDisplay();
        }
        
        // Timer
        let timerInterval;
        let totalSeconds = 0;
        let isRunning = false;
        
        function updateTimerDisplay() {
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;
            document.getElementById('time-left').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        function startTimer() {
            if (!isRunning) {
                if (totalSeconds === 0) {
                    const minutes = parseInt(document.getElementById('minutes').value) || 0;
                    const seconds = parseInt(document.getElementById('seconds').value) || 0;
                    totalSeconds = minutes * 60 + seconds;
                    if (totalSeconds === 0) return;
                }
                
                isRunning = true;
                document.getElementById('start-btn').disabled = true;
                document.getElementById('pause-btn').disabled = false;
                
                timerInterval = setInterval(() => {
                    if (totalSeconds > 0) {
                        totalSeconds--;
                        updateTimerDisplay();
                    } else {
                        clearInterval(timerInterval);
                        alert('Čas vypršel!');
                        resetTimer();
                    }
                }, 1000);
            }
        }
        
        function pauseTimer() {
            clearInterval(timerInterval);
            isRunning = false;
            document.getElementById('start-btn').disabled = false;
            document.getElementById('pause-btn').disabled = true;
        }
        
        function resetTimer() {
            clearInterval(timerInterval);
            isRunning = false;
            totalSeconds = 0;
            document.getElementById('minutes').value = 0;
            document.getElementById('seconds').value = 0;
            document.getElementById('start-btn').disabled = false;
            document.getElementById('pause-btn').disabled = true;
            updateTimerDisplay();
        }
        
        // Inicializace
        updateDisplay();
        updateTimerDisplay();
    </script>
</body>
</html>